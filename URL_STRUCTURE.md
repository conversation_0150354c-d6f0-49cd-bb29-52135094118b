# URL Struktura - Infauna

## Nová URL struktura

### <PERSON><PERSON><PERSON><PERSON> s<PERSON>
- `/inzerce` - Všechny inzeráty s možností filtrování
- `/inzerce/<category-slug>` - Inzer<PERSON>ty podle kategorie (např. `/inzerce/psi`)
- `/inzerce/<category-slug>/<breed-slug>` - Inzeráty podle kategorie a plemena (např. `/inzerce/psi/labradorsky-retrivr`)

### Dostupné kategorie
- `psi` - Psi
- `kocky` - <PERSON><PERSON><PERSON>  
- `ptaci` - Ptáci
- `hlodavci` - Hlodavci
- `ryby` - Ryby
- `plazi` - Plazi
- `ostatni` - Ostatní

### Příklady URL pro plemena

#### Psi
- `/inzerce/psi/labradorsky-retrivr` - Labradorský retrívr
- `/inzerce/psi/nemecky-ovcak` - Německ<PERSON> ovč<PERSON>
- `/inzerce/psi/golden-retrivr` - Golden retrívr
- `/inzerce/psi/francouzsky-buldocek` - <PERSON>uz<PERSON><PERSON> buldoček
- `/inzerce/psi/beagle` - Beagle
- `/inzerce/psi/husky` - Husky
- `/inzerce/psi/chihuahua` - Chihuahua
- `/inzerce/psi/mops` - Mops
- `/inzerce/psi/border-kolie` - Border kolie
- `/inzerce/psi/jezevcik` - Jezevčík

#### Kočky
- `/inzerce/kocky/britska-kratkosrsta` - Britská krátkosrstá
- `/inzerce/kocky/perska` - Perská
- `/inzerce/kocky/maine-coon` - Maine Coon
- `/inzerce/kocky/ragdoll` - Ragdoll
- `/inzerce/kocky/siamska` - Siamská
- `/inzerce/kocky/bengalska` - Bengálská
- `/inzerce/kocky/ruska-modra` - Ruská modrá
- `/inzerce/kocky/norska-lesni` - Norská lesní
- `/inzerce/kocky/sphynx` - Sphynx
- `/inzerce/kocky/domaci-kocka` - Domácí kočka

#### Ptáci
- `/inzerce/ptaci/kanarek` - Kanárek
- `/inzerce/ptaci/papousek` - Papoušek
- `/inzerce/ptaci/andulka` - Andulka
- `/inzerce/ptaci/zebricka` - Zebřička
- `/inzerce/ptaci/kakadu` - Kakadu
- `/inzerce/ptaci/ara` - Ara

#### Hlodavci
- `/inzerce/hlodavci/kralik` - Králík
- `/inzerce/hlodavci/morce` - Morče
- `/inzerce/hlodavci/krecek` - Křeček
- `/inzerce/hlodavci/potkan` - Potkan
- `/inzerce/hlodavci/mys` - Myš
- `/inzerce/hlodavci/cincila` - Činčila

## Přesměrování starých URL

### Automatické přesměrování
- `/vyhledavani` → `/inzerce`
- `/kategorie/<slug>` → `/inzerce/<slug>`

### Middleware
Implementováno v `src/middleware.ts` pro automatické přesměrování starých URL na nové.

## Navigace a filtry

### URL parametry pro filtry
Filtry se propisují do URL jako query parametry (kategorie a plemeno jsou součástí URL struktury):
- `regionId` - ID kraje
- `adType` - Typ inzerátu (prodam, koupim, daruji) - bez diakritiky
- `country` - Země (CR, SR, EU) - bez diakritiky
- `search` - Vyhledávací text

### Příklady URL s filtry
- `/inzerce?adType=prodam&regionId=1`
- `/inzerce/psi?regionId=5&adType=prodam&country=CR`
- `/inzerce/psi/labradorsky-retrivr?adType=prodam&country=CR`

### Breadcrumb navigace
- Automaticky generovaná podle URL struktury
- Komponenta: `src/components/ui/Breadcrumb.tsx`
- Utility funkce: `src/lib/url-utils.ts`

### Filtry s URL synchronizací
- Komponenta: `src/components/ads/AdsFiltersWithNavigation.tsx`
- Všechny filtry se automaticky propisují do URL
- Možnost sdílení odkazů na konkrétní filtrované výsledky

### Grid kategorií
- **CategoryGrid**: Grid s kategoriemi na hlavní stránce `/inzerce`
- Vždy se zobrazuje na hlavní stránce
- Kliknutí na kategorii naviguje na `/inzerce/<category-slug>`

### Rychlé odkazy
- **BreedQuickLinks**: Rychlé odkazy na plemena (stránky kategorií)
- Navigují přímo na `/inzerce/<category>/<breed>` stránky

### Filtrování
- **Na `/inzerce`**: Pouze základní filtry (bez kategorie a plemena)
- **Na `/inzerce/<category>`**: Základní filtry + filtr plemene
- **Na `/inzerce/<category>/<breed>`**: Pouze základní filtry (kategorie a plemeno jsou fixní)

### Aktivní filtry
- **ActiveFilters**: Zobrazení aktivních filtrů s možností odstranění
- Zobrazuje se nad výpisem inzerátů
- Možnost odstranit jednotlivé filtry nebo všechny najednou

## Implementované komponenty

### Stránky
- `src/app/inzerce/page.tsx` - Hlavní stránka inzerátů
- `src/app/inzerce/[category]/page.tsx` - Stránka kategorie
- `src/app/inzerce/[category]/[breed]/page.tsx` - Stránka kategorie + plemeno

### Komponenty
- `src/components/ads/AdsFiltersWithNavigation.tsx` - Filtry s URL synchronizací
- `src/components/ads/CategoryGrid.tsx` - Grid kategorií na hlavní stránce
- `src/components/ads/BreedQuickLinks.tsx` - Rychlé odkazy na plemena
- `src/components/ads/ActiveFilters.tsx` - Zobrazení aktivních filtrů
- `src/components/ui/Breadcrumb.tsx` - Breadcrumb navigace

### Utility funkce
- `src/lib/url-utils.ts` - Funkce pro generování URL a breadcrumbs

## SEO optimalizace

### Metadata
- Dynamicky generovaná metadata pro každou stránku
- Optimalizované title a description
- Strukturované URL pro lepší SEO

### URL struktura
- Čisté, SEO-friendly URL
- Hierarchická struktura kategorie/plemeno
- Automatické přesměrování pro zachování SEO hodnoty

## Testování

### Funkční testování
1. Navigace mezi stránkami
2. Filtrování podle kategorie a plemena
3. Breadcrumb navigace
4. Rychlé odkazy
5. Přesměrování starých URL

### URL testování
- Ověřit všechny URL kombinace
- Testovat přesměrování
- Kontrola 404 stránek pro neexistující kategorie/plemena
