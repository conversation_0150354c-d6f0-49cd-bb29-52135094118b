const { db, users } = require('../src/db/index.ts');

async function createTestUser() {
  try {
    console.log('Creating test user...');

    const testUser = {
      id: 'test-user-id',
      phoneNumber: '+420123456789',
    };

    const [newUser] = await db
      .insert(users)
      .values(testUser)
      .onConflictDoNothing()
      .returning();

    if (newUser) {
      console.log('✓ Test user created:', newUser);
    } else {
      console.log('✓ Test user already exists');
    }
  } catch (error) {
    console.error('Error creating test user:', error);
  }
}

createTestUser();
