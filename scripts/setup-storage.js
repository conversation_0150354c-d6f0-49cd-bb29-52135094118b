const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://dbmbjzabnvokmchopbsc.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRibWJqemFibnZva21jaG9wYnNjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTExMjI4OSwiZXhwIjoyMDY0Njg4Mjg5fQ.7w4LPZrgLwUqtnMruoY78ph4PiqQdoYvYfMN_2HHwPQ';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupStorage() {
  try {
    console.log('Setting up Supabase storage...');

    // Create images bucket
    const { data: bucket, error: bucketError } = await supabase.storage.createBucket('images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
      fileSizeLimit: 5242880, // 5MB
    });

    if (bucketError && bucketError.message !== 'Bucket already exists') {
      console.error('Error creating bucket:', bucketError);
      return;
    }

    console.log('✓ Images bucket created/exists');

    // Set up RLS policies for the bucket
    const policies = [
      {
        name: 'Allow public read access',
        sql: `
          CREATE POLICY "Allow public read access" ON storage.objects
          FOR SELECT USING (bucket_id = 'images');
        `
      },
      {
        name: 'Allow authenticated users to upload',
        sql: `
          CREATE POLICY "Allow authenticated users to upload" ON storage.objects
          FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.role() = 'authenticated');
        `
      },
      {
        name: 'Allow users to update their own images',
        sql: `
          CREATE POLICY "Allow users to update their own images" ON storage.objects
          FOR UPDATE USING (bucket_id = 'images' AND auth.role() = 'authenticated');
        `
      },
      {
        name: 'Allow users to delete their own images',
        sql: `
          CREATE POLICY "Allow users to delete their own images" ON storage.objects
          FOR DELETE USING (bucket_id = 'images' AND auth.role() = 'authenticated');
        `
      }
    ];

    for (const policy of policies) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: policy.sql });
        if (error && !error.message.includes('already exists')) {
          console.error(`Error creating policy "${policy.name}":`, error);
        } else {
          console.log(`✓ Policy "${policy.name}" created/exists`);
        }
      } catch (err) {
        console.log(`Policy "${policy.name}" might already exist`);
      }
    }

    console.log('✓ Storage setup complete!');
  } catch (error) {
    console.error('Setup failed:', error);
  }
}

setupStorage();
