'use client';

import { useState } from 'react';
import { Metadata } from 'next';
import { AdsList } from '@/components/ads/AdsList';
import { AdsFilters } from '@/components/ads/AdsFiltersNew';
import { AdFilters, PaginationParams } from '@/types';
import { Search } from 'lucide-react';

export default function SearchPage() {
  const [filters, setFilters] = useState<AdFilters>({});
  const [pagination] = useState<PaginationParams>({
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  const handleFiltersChange = (newFilters: AdFilters) => {
    setFilters(newFilters);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Search className="w-8 h-8 text-primary-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">
              Vyhledávání inzerátů
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Najděte přesně to, co hledáte pomocí pokročilých filtrů
          </p>
        </div>

        {/* Filters and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <AdsFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </div>

          {/* Ads Grid */}
          <div className="lg:col-span-3">
            {/* Ads List */}
            <AdsList filters={filters} pagination={pagination} />
          </div>
        </div>
      </div>
    </div>
  );
}
