import { NextRequest, NextResponse } from 'next/server';
import { db, ads } from '@/db';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const adId = parseInt(id);
    
    if (isNaN(adId)) {
      return NextResponse.json(
        { error: 'Invalid ad ID' },
        { status: 400 }
      );
    }

    // Get contact phone for the ad
    const [ad] = await db
      .select({
        id: ads.id,
        contactPhone: ads.contactPhone,
        isActive: ads.isActive,
      })
      .from(ads)
      .where(eq(ads.id, adId));

    if (!ad) {
      return NextResponse.json(
        { error: 'Ad not found' },
        { status: 404 }
      );
    }

    if (!ad.isActive) {
      return NextResponse.json(
        { error: 'Ad is not active' },
        { status: 404 }
      );
    }

    // Log the contact request for analytics (optional)
    console.log(`Contact requested for ad ${adId} at ${new Date().toISOString()}`);

    return NextResponse.json({
      contactPhone: ad.contactPhone,
    });
  } catch (error) {
    console.error('Error fetching contact:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
