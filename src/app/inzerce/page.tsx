'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdsList } from '@/components/ads/AdsList';
import { AdsFiltersWithNavigation } from '@/components/ads/AdsFiltersWithNavigation';
import { CategoryGrid } from '@/components/ads/CategoryGrid';
import { ActiveFilters } from '@/components/ads/ActiveFilters';
import { AdFilters, PaginationParams } from '@/types';
import { Search } from 'lucide-react';

export default function AdsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Parse filters from URL parameters
  const urlFilters = useMemo((): AdFilters => {
    const filters: AdFilters = {};

    if (searchParams.get('regionId')) {
      filters.regionId = parseInt(searchParams.get('regionId')!);
    }
    if (searchParams.get('adType')) {
      filters.adType = searchParams.get('adType') as any;
    }
    if (searchParams.get('country')) {
      filters.country = searchParams.get('country') as any;
    }
    if (searchParams.get('search')) {
      filters.search = searchParams.get('search')!;
    }

    return filters;
  }, [searchParams]);

  const [filters, setFilters] = useState<AdFilters>(urlFilters);
  const [pagination] = useState<PaginationParams>({
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // Update filters when URL changes
  useEffect(() => {
    setFilters(urlFilters);
  }, [urlFilters]);

  const handleFiltersChange = (newFilters: AdFilters) => {
    setFilters(newFilters);

    // Update URL with filters
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        params.set(key, value.toString());
      }
    });

    const newUrl = params.toString() ? `/inzerce?${params.toString()}` : '/inzerce';
    router.push(newUrl, { scroll: false });
  };

  const handleFilterRemove = (key: keyof AdFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    handleFiltersChange(newFilters);
  };

  const handleClearAllFilters = () => {
    handleFiltersChange({});
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Search className="w-8 h-8 text-primary-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">
              Všechny inzeráty
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Procházejte všechny dostupné inzeráty nebo použijte filtry pro upřesnění vyhledávání
          </p>
        </div>

        {/* Category Grid - always show on main page */}
        <div className="mb-8">
          <CategoryGrid />
        </div>

        {/* Filters and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            <AdsFiltersWithNavigation
              filters={filters}
              onFiltersChange={handleFiltersChange}
              enableUrlNavigation={false}
            />
          </div>

          {/* Ads Grid */}
          <div className="lg:col-span-3">
            {/* Active Filters */}
            <ActiveFilters
              filters={filters}
              onFilterRemove={handleFilterRemove}
              onClearAll={handleClearAllFilters}
              excludeKeys={['categoryId', 'breedId']}
            />

            {/* Ads List */}
            <AdsList filters={filters} pagination={pagination} />
          </div>
        </div>
      </div>
    </div>
  );
}
