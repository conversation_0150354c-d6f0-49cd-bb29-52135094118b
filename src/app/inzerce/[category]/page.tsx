import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { db, categories } from '@/db';
import { eq } from 'drizzle-orm';
import { CategoryPageClient } from './CategoryPageClient';

interface CategoryPageProps {
  params: {
    category: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, resolvedParams.category));

  if (!category) {
    return {
      title: 'Kategorie nenalezena',
    };
  }

  return {
    title: `${category.name} - Infauna`,
    description: `Inzeráty v kategorii ${category.name}. Najděte nebo nabídněte zvířata.`,
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, resolvedParams.category));

  if (!category) {
    notFound();
  }

  return <CategoryPageClient category={category} />;
}
