'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AdsList } from '@/components/ads/AdsList';
import { AdsFiltersWithNavigation } from '@/components/ads/AdsFiltersWithNavigation';
import { BreedQuickLinks } from '@/components/ads/BreedQuickLinks';
import { ActiveFilters } from '@/components/ads/ActiveFilters';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { AdFilters, PaginationParams } from '@/types';
import { generateBreadcrumbs } from '@/lib/url-utils';

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface CategoryPageClientProps {
  category: Category;
}

export function CategoryPageClient({ category }: CategoryPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [filters, setFilters] = useState<AdFilters>({ categoryId: category.id });
  const [pagination] = useState<PaginationParams>({
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // Parse URL parameters on mount
  useEffect(() => {
    const urlFilters: AdFilters = { categoryId: category.id };

    if (searchParams.get('breedId')) {
      urlFilters.breedId = parseInt(searchParams.get('breedId')!);
    }
    if (searchParams.get('regionId')) {
      urlFilters.regionId = parseInt(searchParams.get('regionId')!);
    }
    if (searchParams.get('adType')) {
      urlFilters.adType = searchParams.get('adType') as any;
    }
    if (searchParams.get('country')) {
      urlFilters.country = searchParams.get('country') as any;
    }

    if (searchParams.get('search')) {
      urlFilters.search = searchParams.get('search')!;
    }

    setFilters(urlFilters);
  }, [searchParams, category.id]);

  const handleFiltersChange = (newFilters: AdFilters) => {
    const filtersWithCategory = { ...newFilters, categoryId: category.id };
    setFilters(filtersWithCategory);

    // Update URL with filters (exclude categoryId and breedId from URL params)
    const params = new URLSearchParams();
    Object.entries(filtersWithCategory).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && key !== 'categoryId' && key !== 'breedId') {
        params.set(key, value.toString());
      }
    });

    // If breed is selected, navigate to breed page
    if (filtersWithCategory.breedId) {
      // Find breed slug
      fetch(`/api/breeds?categoryId=${category.id}`)
        .then(res => res.json())
        .then(breeds => {
          const breed = breeds.find((b: any) => b.id === filtersWithCategory.breedId);
          if (breed) {
            const breedUrl = params.toString()
              ? `/inzerce/${category.slug}/${breed.slug}?${params.toString()}`
              : `/inzerce/${category.slug}/${breed.slug}`;
            router.push(breedUrl, { scroll: false });
          }
        });
    } else {
      const newUrl = params.toString() ? `/inzerce/${category.slug}?${params.toString()}` : `/inzerce/${category.slug}`;
      router.push(newUrl, { scroll: false });
    }
  };

  const handleFilterRemove = (key: keyof AdFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    // Keep category
    newFilters.categoryId = category.id;
    handleFiltersChange(newFilters);
  };

  const handleClearAllFilters = () => {
    handleFiltersChange({ categoryId: category.id });
  };

  const breadcrumbs = generateBreadcrumbs(category.slug, undefined, [category]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbs} />

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {category.name}
          </h1>
          <p className="text-lg text-gray-600">
            Procházejte inzeráty v kategorii {category.name}
          </p>
        </div>

        {/* Filters and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            <AdsFiltersWithNavigation
              filters={filters}
              onFiltersChange={handleFiltersChange}
              categoryId={category.id}
              enableUrlNavigation={false}
            />

            <BreedQuickLinks
              categoryId={category.id}
              categorySlug={category.slug}
            />
          </div>

          {/* Ads Grid */}
          <div className="lg:col-span-3">
            {/* Active Filters */}
            <ActiveFilters
              filters={filters}
              onFilterRemove={handleFilterRemove}
              onClearAll={handleClearAllFilters}
              excludeKeys={['categoryId', 'breedId']}
            />

            {/* Ads List */}
            <AdsList filters={filters} pagination={pagination} />
          </div>
        </div>
      </div>
    </div>
  );
}
