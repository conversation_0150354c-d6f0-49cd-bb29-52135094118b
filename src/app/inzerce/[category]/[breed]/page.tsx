import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { db, categories, breeds } from '@/db';
import { eq, and } from 'drizzle-orm';
import { BreedPageClient } from './BreedPageClient';

interface BreedPageProps {
  params: {
    category: string;
    breed: string;
  };
}

export async function generateMetadata({ params }: BreedPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  // Get category first
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, resolvedParams.category));

  if (!category) {
    return {
      title: '<PERSON><PERSON><PERSON> nenalezen<PERSON>',
    };
  }

  // Get breed
  const [breed] = await db
    .select()
    .from(breeds)
    .where(and(
      eq(breeds.slug, resolvedParams.breed),
      eq(breeds.categoryId, category.id)
    ));

  if (!breed) {
    return {
      title: 'Plemeno nenalezeno',
    };
  }

  return {
    title: `${breed.name} - ${category.name} - Infauna`,
    description: `Inzeráty pro ${breed.name} v kategorii ${category.name}. Najděte nebo nabídněte zvířata.`,
  };
}

export default async function BreedPage({ params }: BreedPageProps) {
  const resolvedParams = await params;
  // Get category first
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, resolvedParams.category));

  if (!category) {
    notFound();
  }

  // Get breed
  const [breed] = await db
    .select()
    .from(breeds)
    .where(and(
      eq(breeds.slug, resolvedParams.breed),
      eq(breeds.categoryId, category.id)
    ));

  if (!breed) {
    notFound();
  }

  return <BreedPageClient category={category} breed={breed} />;
}
