@import "tailwindcss";

@theme {
  /* Primary color palette based on #48435C */
  --color-primary-50: oklch(0.97 0.008 285);
  --color-primary-100: oklch(0.94 0.016 285);
  --color-primary-200: oklch(0.88 0.032 285);
  --color-primary-300: oklch(0.80 0.048 285);
  --color-primary-400: oklch(0.70 0.064 285);
  --color-primary-500: oklch(0.60 0.080 285);
  --color-primary-600: oklch(0.50 0.096 285); /* Base #48435C */
  --color-primary-700: oklch(0.42 0.080 285);
  --color-primary-800: oklch(0.34 0.064 285);
  --color-primary-900: oklch(0.26 0.048 285);
  --color-primary-950: oklch(0.18 0.032 285);

  /* Accent color palette based on #913048 */
  --color-accent-50: oklch(0.96 0.012 350);
  --color-accent-100: oklch(0.92 0.024 350);
  --color-accent-200: oklch(0.84 0.048 350);
  --color-accent-300: oklch(0.76 0.072 350);
  --color-accent-400: oklch(0.68 0.096 350);
  --color-accent-500: oklch(0.60 0.120 350);
  --color-accent-600: oklch(0.52 0.144 350); /* Base #913048 */
  --color-accent-700: oklch(0.44 0.120 350);
  --color-accent-800: oklch(0.36 0.096 350);
  --color-accent-900: oklch(0.28 0.072 350);
  --color-accent-950: oklch(0.20 0.048 350);
}

/* Custom utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
