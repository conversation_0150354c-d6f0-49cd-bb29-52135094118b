'use client';

import { useState } from 'react';
import { AdsList } from '@/components/ads/AdsList';
import { AdsFilters } from '@/components/ads/AdsFilters';
import { AdFilters, PaginationParams } from '@/types';

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface CategoryPageClientProps {
  category: Category;
}

export function CategoryPageClient({ category }: CategoryPageClientProps) {
  const [filters, setFilters] = useState<AdFilters>({ categoryId: category.id });
  const [pagination] = useState<PaginationParams>({
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  const handleFiltersChange = (newFilters: AdFilters) => {
    setFilters({ ...newFilters, categoryId: category.id });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {category.name}
          </h1>
          <p className="text-lg text-gray-600">
            Procházejte inzeráty v kategorii {category.name}
          </p>
        </div>

        {/* Filters and Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <AdsFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              categoryId={category.id}
            />
          </div>

          {/* Ads Grid */}
          <div className="lg:col-span-3">

            {/* Ads List */}
            <AdsList filters={filters} pagination={pagination} />
          </div>
        </div>
      </div>
    </div>
  );
}
