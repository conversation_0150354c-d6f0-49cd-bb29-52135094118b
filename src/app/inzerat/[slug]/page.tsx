import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { db, ads, categories, breeds, regions, users, adImages } from '@/db';
import { eq } from 'drizzle-orm';
import { formatPrice, formatDate } from '@/lib/utils';
import { MapPin, Calendar, ArrowLeft, Heart, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ContactSection } from '@/components/ads/ContactSection';

interface AdDetailPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: AdDetailPageProps): Promise<Metadata> {
  const { slug } = await params;
  const [ad] = await db
    .select({
      title: ads.title,
      description: ads.description,
    })
    .from(ads)
    .where(eq(ads.slug, slug));

  if (!ad) {
    return {
      title: 'Inzerát nenalezen',
    };
  }

  return {
    title: `${ad.title} - Infauna`,
    description: ad.description.substring(0, 160),
  };
}

export default async function AdDetailPage({ params }: AdDetailPageProps) {
  const { slug } = await params;
  const [ad] = await db
    .select({
      id: ads.id,
      title: ads.title,
      adType: ads.adType,
      country: ads.country,
      priceType: ads.priceType,
      priceAmount: ads.priceAmount,
      description: ads.description,
      createdAt: ads.createdAt,
      updatedAt: ads.updatedAt,
      isActive: ads.isActive,
      slug: ads.slug,
      category: {
        id: categories.id,
        name: categories.name,
        slug: categories.slug,
      },
      breed: {
        id: breeds.id,
        name: breeds.name,
        slug: breeds.slug,
      },
      region: {
        id: regions.id,
        name: regions.name,
        slug: regions.slug,
      },
      user: {
        id: users.id,
        phoneNumber: users.phoneNumber,
      },
    })
    .from(ads)
    .leftJoin(categories, eq(ads.categoryId, categories.id))
    .leftJoin(breeds, eq(ads.breedId, breeds.id))
    .leftJoin(regions, eq(ads.regionId, regions.id))
    .leftJoin(users, eq(ads.userId, users.id))
    .where(eq(ads.slug, slug));

  if (!ad || !ad.isActive) {
    notFound();
  }

  // Get images
  const images = await db
    .select()
    .from(adImages)
    .where(eq(adImages.adId, ad.id))
    .orderBy(adImages.order);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link 
            href={`/kategorie/${ad.category.slug}`}
            className="inline-flex items-center text-primary-600 hover:text-primary-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Zpět na {ad.category.name}
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Images */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              {images.length > 0 ? (
                <div className="aspect-video relative">
                  <Image
                    src={images[0].imageUrl}
                    alt={ad.title}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="aspect-video bg-gray-200 flex items-center justify-center">
                  <span className="text-6xl text-gray-400">📷</span>
                </div>
              )}
              
              {/* Thumbnail Gallery */}
              {images.length > 1 && (
                <div className="p-4 grid grid-cols-4 gap-2">
                  {images.slice(1, 5).map((image, index) => (
                    <div key={image.id} className="aspect-square relative rounded overflow-hidden">
                      <Image
                        src={image.imageUrl}
                        alt={`${ad.title} - obrázek ${index + 2}`}
                        fill
                        className="object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Description */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Popis
              </h2>
              <div className="prose prose-gray max-w-none">
                <p className="whitespace-pre-wrap">{ad.description}</p>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-8">
              {/* Title and Type */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                    ad.adType === 'prodám' 
                      ? 'bg-green-100 text-green-800'
                      : ad.adType === 'koupím'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    {ad.adType}
                  </span>
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="icon">
                      <Heart className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {ad.title}
                </h1>
              </div>

              {/* Price */}
              {ad.priceType && ad.adType !== 'daruji' && (
                <div className="mb-6">
                  <div className="text-3xl font-bold text-primary-600">
                    {ad.priceAmount && (ad.priceType === 'CZK' || ad.priceType === 'EUR')
                      ? formatPrice(ad.priceAmount, ad.priceType)
                      : ad.priceType
                    }
                  </div>
                </div>
              )}

              {/* Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-gray-600">
                  <span className="font-medium text-gray-900 w-20">Kategorie:</span>
                  <Link 
                    href={`/kategorie/${ad.category.slug}`}
                    className="text-primary-600 hover:text-primary-700"
                  >
                    {ad.category.name}
                  </Link>
                </div>
                
                {ad.breed && (
                  <div className="flex items-center text-gray-600">
                    <span className="font-medium text-gray-900 w-20">Plemeno:</span>
                    <span>{ad.breed.name}</span>
                  </div>
                )}
                
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-4 h-4 mr-2" />
                  <span>{ad.country}</span>
                  {ad.region && (
                    <>
                      <span className="mx-1">•</span>
                      <span>{ad.region.name}</span>
                    </>
                  )}
                </div>
                
                <div className="flex items-center text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>Přidáno {formatDate(ad.createdAt)}</span>
                </div>
              </div>

              {/* Contact */}
              <ContactSection adId={ad.id} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
