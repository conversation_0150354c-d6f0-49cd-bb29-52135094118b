import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';
import { Plus, Search, Heart, Shield, Users, Sparkles, Award, Clock } from 'lucide-react';
import { AnimatedBackground } from '@/components/ui/AnimatedBackground';
import { StatsSection } from '@/components/ui/StatsSection';
import { TestimonialsSection } from '@/components/ui/TestimonialsSection';
import { RecentAds } from '@/components/ads/RecentAds';
import { db, categories } from '@/db';

export default async function HomePage() {
  // Use static categories for now - database works via API
  const allCategories = [
    { id: 1, name: 'P<PERSON>', slug: 'psi' },
    { id: 2, name: '<PERSON><PERSON><PERSON>', slug: 'kocky' },
    { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'ptaci' },
    { id: 4, name: 'Ostatní', slug: 'ostatni' },
  ];
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 text-white overflow-hidden">
        {/* Animated Background */}
        <AnimatedBackground />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-sm font-medium mb-8">
              <Sparkles className="w-4 h-4 mr-2" />
              Nejdůvěryhodnější portál pro zvířata
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
              Najděte svého nového
              <span className="block bg-gradient-to-r from-accent-200 to-white bg-clip-text text-transparent">
                společníka
              </span>
            </h1>

            <p className="text-xl md:text-2xl mb-12 text-primary-100 max-w-4xl mx-auto leading-relaxed">
              Největší inzertní portál pro zvířata v České republice, na Slovensku a v EU.
              Tisíce spokojených majitelů a jejich nových společníků.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button size="lg" className="bg-white text-primary-700 hover:bg-primary-50 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300" asChild>
                <Link href="/inzerce">
                  <Search className="w-5 h-5 mr-2" />
                  Procházet inzeráty
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary-700 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300" asChild>
                <Link href="/pridat-inzerat">
                  <Plus className="w-5 h-5 mr-2" />
                  Přidat inzerát
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium mb-4">
              <Heart className="w-4 h-4 mr-2" />
              Kategorie zvířat
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Najděte svého
              <span className="text-accent-600"> dokonalého</span> společníka
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Vyberte si kategorii a objevte tisíce milujících zvířat, která čekají na nový domov
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {allCategories.map((category, index) => {
              const icons = ['🐕', '🐱', '🦜', '🐰', '🐠', '🦎', '🐹'];
              const descriptions = [
                'Štěňata i dospělí psi všech plemen',
                'Koťata i dospělé kočky všech plemen',
                'Papoušci, kanáři a další ptactvo',
                'Králíci, morčata a další hlodavci',
                'Akvarijní a jezírkové ryby',
                'Plazi a obojživelníci',
                'Ostatní domácí zvířata'
              ];

              return (
                <CategoryCard
                  key={category.id}
                  title={category.name}
                  slug={category.slug}
                  description={descriptions[index] || 'Různá zvířata a jejich potřeby'}
                  icon={icons[index] || '🐾'}
                />
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Features Section */}
      <section className="py-24 bg-primary-900 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-accent-400 rounded-full"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-primary-400 rounded-full"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-3 py-1 bg-accent-600/20 text-accent-200 rounded-full text-sm font-medium mb-4">
              <Award className="w-4 h-4 mr-2" />
              Proč jsme nejlepší
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Proč zvolit
              <span className="text-accent-300"> Infaunu?</span>
            </h2>
            <p className="text-xl text-primary-200 max-w-3xl mx-auto">
              Jsme tu pro vás a vaše zvířata s nejlepšími službami a péčí
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Heart className="w-10 h-10 text-accent-400" />}
              title="S láskou ke zvířatům"
              description="Každý inzerát prochází pečlivou kontrolou, aby byla zajištěna nejlepší péče o zvířata"
            />
            <FeatureCard
              icon={<Shield className="w-10 h-10 text-accent-400" />}
              title="Bezpečné transakce"
              description="Ověření prodejci a bezpečné platební metody pro vaši maximální ochranu"
            />
            <FeatureCard
              icon={<Users className="w-10 h-10 text-accent-400" />}
              title="Aktivní komunita"
              description="Tisíce spokojených zákazníků a úspěšných adopcí každý měsíc po celé EU"
            />
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Recent Ads Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-sm font-medium mb-4">
              <Clock className="w-4 h-4 mr-2" />
              Nejnovější nabídky
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Nejnovější
              <span className="text-accent-600"> inzeráty</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Objevte nejnovější nabídky zvířat od ověřených prodejců
            </p>
          </div>

          {/* Recent ads loaded from API */}
          <RecentAds />
        </div>
      </section>
    </div>
  );
}

function CategoryCard({ title, slug, description, icon }: {
  title: string;
  slug: string;
  description: string;
  icon: string;
}) {
  return (
    <Link href={`/inzerce/${slug}`} className="group">
      <div className="relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-8 text-center border border-gray-100 hover:border-accent-200 transform hover:-translate-y-2 overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50 to-accent-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div className="relative z-10">
          <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">{icon}</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-accent-600 transition-colors duration-300">
            {title}
          </h3>
          <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300 leading-relaxed">{description}</p>

          {/* Arrow indicator */}
          <div className="mt-6 inline-flex items-center text-primary-600 group-hover:text-accent-600 font-medium">
            <span className="mr-2">Prohlédnout</span>
            <svg className="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    </Link>
  );
}

function FeatureCard({ icon, title, description }: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center group">
      <div className="flex justify-center mb-6">
        <div className="p-4 bg-white/10 rounded-2xl backdrop-blur-sm group-hover:bg-accent-600/20 transition-all duration-300 group-hover:scale-110">
          {icon}
        </div>
      </div>
      <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-accent-200 transition-colors duration-300">
        {title}
      </h3>
      <p className="text-primary-200 leading-relaxed group-hover:text-white transition-colors duration-300">
        {description}
      </p>
    </div>
  );
}
