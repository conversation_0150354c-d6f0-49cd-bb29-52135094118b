import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  // Redirect old search page to new ads page
  if (pathname === '/vyhledavani') {
    return NextResponse.redirect(new URL('/inzerce' + search, request.url));
  }

  // Redirect old category pages to new structure
  if (pathname.startsWith('/kategorie/')) {
    const categorySlug = pathname.replace('/kategorie/', '');
    return NextResponse.redirect(new URL(`/inzerce/${categorySlug}` + search, request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/vyhledavani',
    '/kategorie/:path*'
  ]
};
