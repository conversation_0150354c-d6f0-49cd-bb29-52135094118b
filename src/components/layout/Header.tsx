'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Menu, X, Plus, User, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/components/auth/AuthProvider';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signOut, loading } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-18">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent hover:from-accent-600 hover:to-primary-600 transition-all duration-300">
              Infauna
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-gray-700 hover:text-accent-600 px-4 py-3 text-sm font-medium rounded-lg hover:bg-accent-50 transition-all duration-200">
              Domů
            </Link>
            <Link href="/inzerce" className="text-gray-700 hover:text-accent-600 px-4 py-3 text-sm font-medium rounded-lg hover:bg-accent-50 transition-all duration-200">
              Inzeráty
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button className="bg-accent-600 hover:bg-accent-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300" asChild>
              <Link href="/pridat-inzerat">
                <Plus className="w-4 h-4 mr-2" />
                Přidat inzerát
              </Link>
            </Button>

            {user ? (
              <div className="flex items-center space-x-2">
                <Button variant="outline" asChild>
                  <Link href="/profil">
                    <User className="w-4 h-4 mr-2" />
                    Profil
                  </Link>
                </Button>
                <Button variant="ghost" onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Odhlásit
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="outline" asChild>
                  <Link href="/auth/login">
                    <User className="w-4 h-4 mr-2" />
                    Přihlásit
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/auth/signup">
                    Registrace
                  </Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-primary-600 p-2"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t">
              <Link
                href="/"
                className="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Domů
              </Link>
              <Link
                href="/inzerce"
                className="text-gray-700 hover:text-primary-600 block px-3 py-2 text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Inzeráty
              </Link>
              <div className="pt-4 pb-3 border-t border-gray-200">
                <div className="flex flex-col space-y-2">
                  <Button asChild className="w-full">
                    <Link href="/pridat-inzerat" onClick={() => setIsMenuOpen(false)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Přidat inzerát
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="w-full">
                    <Link href="/profil" onClick={() => setIsMenuOpen(false)}>
                      <User className="w-4 h-4 mr-2" />
                      Profil
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
