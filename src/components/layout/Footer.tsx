import Link from 'next/link';

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-primary-900 to-primary-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and description */}
          <div className="col-span-1 md:col-span-2">
            <Link href="/" className="text-3xl font-bold bg-gradient-to-r from-white to-accent-200 bg-clip-text text-transparent">
              Infauna
            </Link>
            <p className="mt-6 text-primary-200 max-w-md leading-relaxed">
              Největší inzertní portál pro zvířata v České republice, na Slovensku a v EU.
              Najděte svého nového společníka nebo nabídněte zvířata k prodeji.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-bold text-accent-300 tracking-wider uppercase mb-6">
              Rych<PERSON> odkazy
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href="/kategorie" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Kategorie
                </Link>
              </li>
              <li>
                <Link href="/inzerce" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Vyhledávání
                </Link>
              </li>
              <li>
                <Link href="/pridat-inzerat" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Přidat inzerát
                </Link>
              </li>
              <li>
                <Link href="/profil" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Můj profil
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-sm font-bold text-accent-300 tracking-wider uppercase mb-6">
              Podpora
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href="/napoveda" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Nápověda
                </Link>
              </li>
              <li>
                <Link href="/kontakt" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Kontakt
                </Link>
              </li>
              <li>
                <Link href="/podminky" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Podmínky použití
                </Link>
              </li>
              <li>
                <Link href="/soukromi" className="text-primary-200 hover:text-white transition-colors duration-200 flex items-center group">
                  <span className="w-1.5 h-1.5 bg-accent-400 rounded-full mr-3 group-hover:bg-white transition-colors duration-200"></span>
                  Ochrana soukromí
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-primary-700">
          <p className="text-primary-300 text-center">
            &copy; {new Date().getFullYear()} Infauna. Všechna práva vyhrazena.
          </p>
        </div>
      </div>
    </footer>
  );
}
