'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface Category {
  id: number;
  name: string;
  slug: string;
}

export function CategoryGrid() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(data => {
        setCategories(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching categories:', error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Kategorie zvířat</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Category icons mapping
  const categoryIcons: { [key: string]: string } = {
    'psi': '🐕',
    'kocky': '🐱',
    'ptaci': '🦜',
    'hlodavci': '🐰',
    'ryby': '🐠',
    'plazi': '🦎',
    'ostatni': '🐾'
  };

  const categoryDescriptions: { [key: string]: string } = {
    'psi': 'Štěňata i dospělí psi všech plemen',
    'kocky': 'Koťata i dospělé kočky všech plemen',
    'ptaci': 'Papoušci, kanáři a další ptactvo',
    'hlodavci': 'Králíci, morčata a další hlodavci',
    'ryby': 'Akvarijní a jezírkové ryby',
    'plazi': 'Plazi a obojživelníci',
    'ostatni': 'Ostatní domácí zvířata'
  };

  const handleCategoryClick = (category: Category) => {
    // Navigate to category page
    router.push(`/inzerce/${category.slug}`);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Kategorie zvířat</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => handleCategoryClick(category)}
            className="group bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-6 text-center border border-gray-200 hover:border-primary-300"
          >
            <div className="text-4xl mb-4">
              {categoryIcons[category.slug] || '🐾'}
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600">
              {category.name}
            </h3>
            <p className="text-gray-600 text-sm">
              {categoryDescriptions[category.slug] || 'Různá zvířata a jejich potřeby'}
            </p>
          </button>
        ))}
      </div>
    </div>
  );
}
