'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronRight } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
}

export function CategoryQuickLinks() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(data => {
        setCategories(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching categories:', error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Kategorie</h3>
        <div className="animate-pulse space-y-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (categories.length === 0) {
    return null;
  }

  // Category icons mapping
  const categoryIcons: { [key: string]: string } = {
    'psi': '🐕',
    'kocky': '🐱',
    'ptaci': '🦜',
    'hlodavci': '🐰',
    'ryby': '🐠',
    'plazi': '🦎',
    'ostatni': '🐾'
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Kategorie zvířat
      </h3>
      
      <div className="space-y-2">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={`/inzerce/${category.slug}`}
            className="flex items-center justify-between p-3 rounded-md hover:bg-gray-50 text-gray-700 hover:text-gray-900 transition-colors"
          >
            <div className="flex items-center">
              <span className="text-2xl mr-3">
                {categoryIcons[category.slug] || '🐾'}
              </span>
              <span className="font-medium">{category.name}</span>
            </div>
            <ChevronRight className="w-4 h-4 text-gray-400" />
          </Link>
        ))}
      </div>
    </div>
  );
}
