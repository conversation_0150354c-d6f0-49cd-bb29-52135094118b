'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Phone, Eye, Loader2, AlertCircle } from 'lucide-react';

interface ContactSectionProps {
  adId: number;
}

export function ContactSection({ adId }: ContactSectionProps) {
  const [contactPhone, setContactPhone] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [revealed, setRevealed] = useState(false);

  const handleRevealContact = async () => {
    if (revealed) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/ads/${adId}/contact`);
      
      if (!response.ok) {
        throw new Error('Nepodařilo se načíst kontakt');
      }

      const data = await response.json();
      setContactPhone(data.contactPhone);
      setRevealed(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : '<PERSON><PERSON><PERSON> při načítání kontaktu');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    if (contactPhone) {
      window.location.href = `tel:${contactPhone}`;
    }
  };

  return (
    <div className="border-t pt-6">
      <h3 className="font-semibold text-gray-900 mb-3">
        Kontakt
      </h3>
      
      <div className="space-y-3">
        {!revealed ? (
          // Show reveal button
          <Button 
            onClick={handleRevealContact}
            disabled={loading}
            variant="outline"
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Načítání...
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Zobrazit kontakt
              </>
            )}
          </Button>
        ) : (
          // Show contact info
          <>
            <div className="flex items-center justify-center p-3 bg-gray-50 rounded-lg">
              <Phone className="w-4 h-4 mr-2 text-gray-400" />
              <span className="font-mono text-lg font-medium">{contactPhone}</span>
            </div>
            
            <Button 
              onClick={handleCall}
              className="w-full"
            >
              <Phone className="w-4 h-4 mr-2" />
              Zavolat
            </Button>
          </>
        )}
        
        {error && (
          <div className="flex items-center text-red-600 text-sm">
            <AlertCircle className="w-4 h-4 mr-2" />
            {error}
          </div>
        )}
      </div>
    </div>
  );
}
