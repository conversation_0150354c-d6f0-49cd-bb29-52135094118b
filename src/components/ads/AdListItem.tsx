import Link from 'next/link';
import Image from 'next/image';
import { formatPrice, formatDate } from '@/lib/utils';
import { AdWithDetails } from '@/types';
import { MapPin, Calendar, Phone, Eye, Heart, ArrowRight } from 'lucide-react';

interface AdListItemProps {
  ad: AdWithDetails;
}

export function AdListItem({ ad }: AdListItemProps) {
  const mainImage = ad.images?.[0]?.imageUrl;
  
  return (
    <Link href={`/inzerat/${ad.slug}`} className="group block">
      <div className="bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-accent-200 transform hover:-translate-y-2 hover:scale-[1.02]">
        <div className="flex flex-col lg:flex-row">
          {/* Image Section */}
          <div className="lg:w-96 lg:flex-shrink-0">
            <div className="aspect-video lg:aspect-[4/3] relative bg-gradient-to-br from-gray-100 to-gray-200">
              {mainImage ? (
                <Image
                  src={mainImage}
                  alt={ad.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <span className="text-6xl mb-2 block">📷</span>
                    <span className="text-sm font-medium">Bez fotografie</span>
                  </div>
                </div>
              )}
              
              {/* Ad Type Badge */}
              <div className="absolute top-4 left-4">
                <span className={`px-3 py-1.5 text-sm font-bold rounded-full backdrop-blur-sm ${
                  ad.adType === 'prodám' 
                    ? 'bg-emerald-500/90 text-white'
                    : ad.adType === 'koupím'
                    ? 'bg-blue-500/90 text-white'
                    : 'bg-purple-500/90 text-white'
                }`}>
                  {ad.adType}
                </span>
              </div>
              
              {/* Price Badge */}
              {ad.priceType && ad.adType !== 'daruji' && (
                <div className="absolute top-4 right-4">
                  <span className="bg-white/95 backdrop-blur-sm px-3 py-1.5 text-lg font-bold rounded-full text-accent-600 shadow-lg">
                    {ad.priceAmount && (ad.priceType === 'CZK' || ad.priceType === 'EUR')
                      ? formatPrice(ad.priceAmount, ad.priceType)
                      : ad.priceType
                    }
                  </span>
                </div>
              )}

              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
          
          {/* Content Section */}
          <div className="flex-1 p-8 lg:p-10">
            <div className="flex flex-col h-full justify-between min-h-[280px] lg:min-h-[320px]">
              {/* Header */}
              <div className="flex-1">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-3xl font-bold text-gray-900 mb-3 group-hover:text-accent-600 transition-colors duration-300 line-clamp-2 leading-tight">
                      {ad.title}
                    </h3>
                    
                    {/* Category and Breed */}
                    <div className="flex items-center mb-3">
                      <span className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                        {ad.category.name}
                      </span>
                      {ad.breed && (
                        <>
                          <ArrowRight className="w-4 h-4 mx-2 text-gray-400" />
                          <span className="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-sm font-medium">
                            {ad.breed.name}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  {/* Action Icons */}
                  <div className="flex items-center space-x-2 ml-4">
                    <button className="p-2 rounded-full bg-gray-100 hover:bg-accent-100 text-gray-600 hover:text-accent-600 transition-colors duration-200">
                      <Heart className="w-5 h-5" />
                    </button>
                    <div className="p-2 rounded-full bg-primary-100 text-primary-600">
                      <Eye className="w-5 h-5" />
                    </div>
                  </div>
                </div>
                
                {/* Description */}
                <p className="text-gray-600 text-xl leading-relaxed mb-8 line-clamp-3">
                  {ad.description}
                </p>
              </div>
              
              {/* Footer */}
              <div className="border-t border-gray-100 pt-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  {/* Meta Information */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1.5 text-accent-500" />
                      <span className="font-medium">{ad.country}</span>
                      {ad.region && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{ad.region.name}</span>
                        </>
                      )}
                    </div>
                    
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1.5 text-accent-500" />
                      <span>{formatDate(ad.createdAt)}</span>
                    </div>
                  </div>
                  
                  {/* Contact Button */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-accent-600 to-accent-700 text-white rounded-full text-sm font-medium group-hover:from-accent-700 group-hover:to-accent-800 transition-all duration-300 shadow-lg hover:shadow-xl">
                      <Phone className="w-4 h-4 mr-2" />
                      Zobrazit kontakt
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
