'use client';

import { useState, useEffect } from 'react';
import { AdFilters } from '@/types';
import { Button } from '@/components/ui/Button';
import { SearchableSelect, SelectOption } from '@/components/ui/SearchableSelect';
import { PriceRangeFilter } from '@/components/ui/PriceRangeFilter';
import { AdvancedSearch } from '@/components/ui/AdvancedSearch';
import { X, Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface AdsFiltersProps {
  filters: AdFilters;
  onFiltersChange: (filters: AdFilters) => void;
  categoryId?: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface Region {
  id: number;
  name: string;
  slug: string;
}

export function AdsFilters({ filters, onFiltersChange, categoryId }: AdsFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [localFilters, setLocalFilters] = useState<AdFilters>(filters);
  const [isExpanded, setIsExpanded] = useState(true);
  const [loadingBreeds, setLoadingBreeds] = useState(false);

  // Fetch categories
  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error);
  }, []);

  // Fetch regions
  useEffect(() => {
    fetch('/api/regions')
      .then(res => res.json())
      .then(setRegions)
      .catch(console.error);
  }, []);

  // Fetch breeds when category changes
  useEffect(() => {
    const selectedCategoryId = categoryId || localFilters.categoryId;
    if (selectedCategoryId) {
      setLoadingBreeds(true);
      fetch(`/api/breeds?categoryId=${selectedCategoryId}`)
        .then(res => res.json())
        .then(setBreeds)
        .catch(console.error)
        .finally(() => setLoadingBreeds(false));
    } else {
      setBreeds([]);
      setLoadingBreeds(false);
    }
  }, [localFilters.categoryId, categoryId]);

  const updateFilter = (key: keyof AdFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    
    // Reset breed when category changes
    if (key === 'categoryId') {
      newFilters.breedId = undefined;
    }
    
    setLocalFilters(newFilters);
    // Auto-apply filters for better UX
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters: AdFilters = categoryId ? { categoryId } : {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    if (key === 'categoryId' && categoryId) return false; // Don't count pre-set category
    return localFilters[key as keyof AdFilters] !== undefined;
  });

  // Convert data to SelectOption format
  const categoryOptions: SelectOption[] = categories.map(cat => ({
    value: cat.id,
    label: cat.name,
    description: cat.slug,
  }));

  const breedOptions: SelectOption[] = breeds.map(breed => ({
    value: breed.id,
    label: breed.name,
  }));

  const regionOptions: SelectOption[] = regions.map(region => ({
    value: region.id,
    label: region.name,
  }));

  const adTypeOptions: SelectOption[] = [
    { value: 'prodám', label: 'Prodám' },
    { value: 'koupím', label: 'Koupím' },
    { value: 'daruji', label: 'Daruji' },
  ];

  const countryOptions: SelectOption[] = [
    { value: 'ČR', label: 'Česká republika' },
    { value: 'SR', label: 'Slovensko' },
    { value: 'EU', label: 'EU' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="w-5 h-5 text-primary-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">
              Filtry
            </h2>
            {hasActiveFilters && (
              <span className="ml-2 bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
                {Object.keys(localFilters).filter(key => {
                  if (key === 'categoryId' && categoryId) return false;
                  return localFilters[key as keyof AdFilters] !== undefined;
                }).length}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4 mr-1" />
                Vymazat
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Filters Content */}
      {isExpanded && (
        <div className="p-6 space-y-6">
          {/* Advanced Search */}
          <AdvancedSearch
            value={localFilters.search || ''}
            onChange={(value) => updateFilter('search', value)}
            onSearch={(value) => updateFilter('search', value)}
            placeholder="Hledat inzeráty..."
          />

          {/* Ad Type */}
          <SearchableSelect
            label="Typ inzerátu"
            options={adTypeOptions}
            value={adTypeOptions.find(opt => opt.value === localFilters.adType) || null}
            onChange={(option) => updateFilter('adType', option?.value)}
            placeholder="Všechny typy"
            isClearable
            isSearchable={false}
          />

          {/* Country */}
          <SearchableSelect
            label="Země"
            options={countryOptions}
            value={countryOptions.find(opt => opt.value === localFilters.country) || null}
            onChange={(option) => updateFilter('country', option?.value)}
            placeholder="Všechny země"
            isClearable
            isSearchable={false}
          />

          {/* Region (only for ČR) */}
          {localFilters.country === 'ČR' && (
            <SearchableSelect
              label="Kraj"
              options={regionOptions}
              value={regionOptions.find(opt => opt.value === localFilters.regionId) || null}
              onChange={(option) => updateFilter('regionId', option?.value)}
              placeholder="Všechny kraje"
              isClearable
              noOptionsMessage="Žádné kraje nenalezeny"
            />
          )}

          {/* Category (only if not pre-set) */}
          {!categoryId && (
            <SearchableSelect
              label="Kategorie"
              options={categoryOptions}
              value={categoryOptions.find(opt => opt.value === localFilters.categoryId) || null}
              onChange={(option) => updateFilter('categoryId', option?.value)}
              placeholder="Všechny kategorie"
              isClearable
              noOptionsMessage="Žádné kategorie nenalezeny"
            />
          )}

          {/* Breed */}
          {(categoryId || localFilters.categoryId) && (
            <SearchableSelect
              label="Plemeno/Druh"
              options={breedOptions}
              value={breedOptions.find(opt => opt.value === localFilters.breedId) || null}
              onChange={(option) => updateFilter('breedId', option?.value)}
              placeholder="Všechna plemena"
              isClearable
              isLoading={loadingBreeds}
              noOptionsMessage="Žádná plemena nenalezena"
            />
          )}

          {/* Price Range */}
          <PriceRangeFilter
            value={{
              min: localFilters.priceMin,
              max: localFilters.priceMax,
            }}
            onChange={(range) => {
              updateFilter('priceMin', range.min);
              updateFilter('priceMax', range.max);
            }}
            currency="CZK"
          />
        </div>
      )}
    </div>
  );
}
