'use client';

import { useState, useEffect } from 'react';
import { AdCard } from './AdCard';
import { AdWithDetails, AdFilters, PaginationParams } from '@/types';
import { Button } from '@/components/ui/Button';
import { Loader2 } from 'lucide-react';

interface AdsListProps {
  filters?: AdFilters;
  pagination?: PaginationParams;
}

export function AdsList({ filters = {}, pagination = {} }: AdsListProps) {
  const [ads, setAds] = useState<AdWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(pagination.page || 1);

  const fetchAds = async (page: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: (pagination.limit || 12).toString(),
        sortBy: pagination.sortBy || 'created_at',
        sortOrder: pagination.sortOrder || 'desc',
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        ),
      });

      const response = await fetch(`/api/ads?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch ads');
      }

      const data = await response.json();
      
      if (append) {
        setAds(prev => [...prev, ...data.ads]);
      } else {
        setAds(data.ads);
      }
      
      setHasMore(data.pagination.hasMore);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('AdsList filters changed:', filters);
    fetchAds(1, false);
  }, [filters, pagination.sortBy, pagination.sortOrder]);

  const loadMore = () => {
    fetchAds(currentPage + 1, true);
  };

  if (loading && ads.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
        <span className="ml-2 text-gray-600">Načítání inzerátů...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">Chyba při načítání inzerátů: {error}</p>
        <Button onClick={() => fetchAds(1, false)}>
          Zkusit znovu
        </Button>
      </div>
    );
  }

  if (ads.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg mb-2">
          Žádné inzeráty nebyly nalezeny.
        </p>
        <p className="text-gray-400">
          Zkuste změnit filtry nebo přidat nový inzerát.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Results count */}
      <div className="mb-6">
        <p className="text-gray-600">
          Zobrazeno {ads.length} inzerátů
        </p>
      </div>

      {/* Ads Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {ads.map((ad) => (
          <AdCard key={ad.id} ad={ad} />
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button 
            onClick={loadMore} 
            disabled={loading}
            variant="outline"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Načítání...
              </>
            ) : (
              'Načíst další'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
