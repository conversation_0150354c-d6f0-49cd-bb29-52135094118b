'use client';

import { useState, useEffect } from 'react';
import { AdFilters } from '@/types';
import { Button } from '@/components/ui/Button';
import { X } from 'lucide-react';

interface ActiveFiltersProps {
  filters: AdFilters;
  onFilterRemove: (key: keyof AdFilters) => void;
  onClearAll: () => void;
  excludeKeys?: (keyof AdFilters)[];
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface Region {
  id: number;
  name: string;
  slug: string;
}

export function ActiveFilters({ filters, onFilterRemove, onClearAll, excludeKeys = [] }: ActiveFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);

  useEffect(() => {
    // Fetch categories
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error);

    // Fetch regions
    fetch('/api/regions')
      .then(res => res.json())
      .then(setRegions)
      .catch(console.error);
  }, []);

  useEffect(() => {
    // Fetch breeds when we have a category
    if (filters.categoryId) {
      fetch(`/api/breeds?categoryId=${filters.categoryId}`)
        .then(res => res.json())
        .then(setBreeds)
        .catch(console.error);
    }
  }, [filters.categoryId]);

  const getFilterLabel = (key: keyof AdFilters, value: any): string => {
    switch (key) {
      case 'categoryId':
        const category = categories.find(c => c.id === value);
        return category ? `Kategorie: ${category.name}` : 'Kategorie';
      case 'breedId':
        const breed = breeds.find(b => b.id === value);
        return breed ? `Plemeno: ${breed.name}` : 'Plemeno';
      case 'regionId':
        const region = regions.find(r => r.id === value);
        return region ? `Kraj: ${region.name}` : 'Kraj';
      case 'adType':
        const adTypeLabels: { [key: string]: string } = {
          'prodam': 'Prodám',
          'koupim': 'Koupím',
          'daruji': 'Daruji'
        };
        return `Typ: ${adTypeLabels[value] || value}`;
      case 'country':
        const countryLabels: { [key: string]: string } = {
          'CR': 'Česká republika',
          'SR': 'Slovensko',
          'EU': 'EU'
        };
        return `Země: ${countryLabels[value] || value}`;

      case 'search':
        return `Hledání: "${value}"`;
      default:
        return `${key}: ${value}`;
    }
  };

  const activeFilters = Object.entries(filters).filter(([key, value]) => {
    return value !== undefined && 
           value !== '' && 
           !excludeKeys.includes(key as keyof AdFilters);
  });

  if (activeFilters.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Aktivní filtry</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="text-xs text-gray-500 hover:text-gray-700"
        >
          Vymazat vše
        </Button>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {activeFilters.map(([key, value]) => (
          <div
            key={key}
            className="inline-flex items-center bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm"
          >
            <span>{getFilterLabel(key as keyof AdFilters, value)}</span>
            <button
              onClick={() => onFilterRemove(key as keyof AdFilters)}
              className="ml-2 hover:bg-primary-100 rounded-full p-0.5"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
