'use client';

import { useState, useEffect } from 'react';
import { AdFilters } from '@/types';
import { Button } from '@/components/ui/Button';
import { SearchableSelect, SelectOption } from '@/components/ui/SearchableSelect';
import { PriceRangeFilter } from '@/components/ui/PriceRangeFilter';
import { AdvancedSearch } from '@/components/ui/AdvancedSearch';
import { X, Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface AdsFiltersProps {
  filters: AdFilters;
  onFiltersChange: (filters: AdFilters) => void;
  categoryId?: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface Region {
  id: number;
  name: string;
  slug: string;
}

export function AdsFilters({ filters, onFiltersChange, categoryId }: AdsFiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [localFilters, setLocalFilters] = useState<AdFilters>(filters);

  // Fetch categories
  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error);
  }, []);

  // Fetch regions
  useEffect(() => {
    fetch('/api/regions')
      .then(res => res.json())
      .then(setRegions)
      .catch(console.error);
  }, []);

  // Fetch breeds when category changes
  useEffect(() => {
    const selectedCategoryId = categoryId || localFilters.categoryId;
    if (selectedCategoryId) {
      fetch(`/api/breeds?categoryId=${selectedCategoryId}`)
        .then(res => res.json())
        .then(setBreeds)
        .catch(console.error);
    } else {
      setBreeds([]);
    }
  }, [localFilters.categoryId, categoryId]);

  const updateFilter = (key: keyof AdFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    
    // Reset breed when category changes
    if (key === 'categoryId') {
      newFilters.breedId = undefined;
    }
    
    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
  };

  const clearFilters = () => {
    const clearedFilters: AdFilters = categoryId ? { categoryId } : {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    if (key === 'categoryId' && categoryId) return false; // Don't count pre-set category
    return localFilters[key as keyof AdFilters] !== undefined;
  });

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          Filtry
        </h2>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4 mr-1" />
            Vymazat
          </Button>
        )}
      </div>
      
      <div className="space-y-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Hledat
          </label>
          <input
            type="text"
            value={localFilters.search || ''}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Zadejte hledaný výraz..."
          />
        </div>

        {/* Ad Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Typ inzerátu
          </label>
          <select
            value={localFilters.adType || ''}
            onChange={(e) => updateFilter('adType', e.target.value || undefined)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Všechny</option>
            <option value="prodám">Prodám</option>
            <option value="koupím">Koupím</option>
            <option value="daruji">Daruji</option>
          </select>
        </div>

        {/* Country */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Země
          </label>
          <select
            value={localFilters.country || ''}
            onChange={(e) => updateFilter('country', e.target.value || undefined)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Všechny</option>
            <option value="ČR">Česká republika</option>
            <option value="SR">Slovensko</option>
            <option value="EU">EU</option>
          </select>
        </div>

        {/* Region (only for ČR) */}
        {localFilters.country === 'ČR' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kraj
            </label>
            <select
              value={localFilters.regionId || ''}
              onChange={(e) => updateFilter('regionId', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Všechny kraje</option>
              {regions.map((region) => (
                <option key={region.id} value={region.id}>
                  {region.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Category (only if not pre-set) */}
        {!categoryId && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kategorie
            </label>
            <select
              value={localFilters.categoryId || ''}
              onChange={(e) => updateFilter('categoryId', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Všechny kategorie</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Breed */}
        {(categoryId || localFilters.categoryId) && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Plemeno/Druh
            </label>
            <select
              value={localFilters.breedId || ''}
              onChange={(e) => updateFilter('breedId', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Všechna plemena</option>
              {breeds.map((breed) => (
                <option key={breed.id} value={breed.id}>
                  {breed.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Price Range */}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cena od (Kč)
            </label>
            <input
              type="number"
              min="0"
              value={localFilters.priceMin || ''}
              onChange={(e) => updateFilter('priceMin', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="0"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cena do (Kč)
            </label>
            <input
              type="number"
              min="0"
              value={localFilters.priceMax || ''}
              onChange={(e) => updateFilter('priceMax', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="100000"
            />
          </div>
        </div>

        {/* Apply Button */}
        <Button onClick={applyFilters} className="w-full">
          Aplikovat filtry
        </Button>
      </div>
    </div>
  );
}
