'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import { AdFilters } from '@/types';
import { Button } from '@/components/ui/Button';
import { SelectOption } from '@/components/ui/SearchableSelect';

const SearchableSelect = dynamic(() => import('@/components/ui/SearchableSelect').then(mod => ({ default: mod.SearchableSelect })), {
  ssr: false,
  loading: () => <div className="h-10 bg-gray-100 rounded animate-pulse" />
});

import { AdvancedSearch } from '@/components/ui/AdvancedSearch';
import { generateAdsUrl } from '@/lib/url-utils';
import { X, Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface AdsFiltersWithNavigationProps {
  filters: AdFilters;
  onFiltersChange: (filters: AdFilters) => void;
  categoryId?: number;
  enableUrlNavigation?: boolean;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface Region {
  id: number;
  name: string;
  slug: string;
}

export function AdsFiltersWithNavigation({ 
  filters, 
  onFiltersChange, 
  categoryId,
  enableUrlNavigation = false 
}: AdsFiltersWithNavigationProps) {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [localFilters, setLocalFilters] = useState<AdFilters>(filters);
  const [isExpanded, setIsExpanded] = useState(true);
  const [loadingBreeds, setLoadingBreeds] = useState(false);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Fetch categories
  useEffect(() => {
    fetch('/api/categories')
      .then(res => res.json())
      .then(setCategories)
      .catch(console.error);
  }, []);

  // Fetch regions
  useEffect(() => {
    fetch('/api/regions')
      .then(res => res.json())
      .then(setRegions)
      .catch(console.error);
  }, []);

  // Fetch breeds when category changes
  useEffect(() => {
    const selectedCategoryId = categoryId || localFilters.categoryId;
    if (selectedCategoryId) {
      setLoadingBreeds(true);
      fetch(`/api/breeds?categoryId=${selectedCategoryId}`)
        .then(res => res.json())
        .then(setBreeds)
        .catch(console.error)
        .finally(() => setLoadingBreeds(false));
    } else {
      setBreeds([]);
      setLoadingBreeds(false);
    }
  }, [localFilters.categoryId, categoryId]);

  const updateFilter = (key: keyof AdFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };

    // Reset breed when category changes
    if (key === 'categoryId') {
      newFilters.breedId = undefined;
    }

    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters: AdFilters = categoryId ? { categoryId } : {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.keys(localFilters).some(key => {
    if (key === 'categoryId' && categoryId) return false; // Don't count pre-set category
    return localFilters[key as keyof AdFilters] !== undefined;
  });

  // Convert data to SelectOption format
  const categoryOptions: SelectOption[] = categories.map(cat => ({
    value: cat.id,
    label: cat.name,
    description: `${cat.slug}`,
  }));

  const breedOptions: SelectOption[] = breeds.map(breed => ({
    value: breed.id,
    label: breed.name,
  }));

  const regionOptions: SelectOption[] = regions.map(region => ({
    value: region.id,
    label: region.name,
  }));

  const adTypeOptions: SelectOption[] = [
    { value: 'prodam', label: 'Prodám' },
    { value: 'koupim', label: 'Koupím' },
    { value: 'daruji', label: 'Daruji' },
  ];

  const countryOptions: SelectOption[] = [
    { value: 'CR', label: 'Česká republika' },
    { value: 'SR', label: 'Slovensko' },
    { value: 'EU', label: 'EU' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="w-5 h-5 text-primary-600 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">
              Filtry
            </h2>
            {hasActiveFilters && (
              <span className="ml-2 bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
                {Object.keys(localFilters).filter(key => {
                  if (key === 'categoryId' && categoryId) return false;
                  return localFilters[key as keyof AdFilters] !== undefined;
                }).length}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4 mr-1" />
                Vymazat
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Filters Content */}
      {isExpanded && (
        <div className="p-6 space-y-6">
          {/* Advanced Search */}
          <AdvancedSearch
            value={localFilters.search || ''}
            onChange={(value) => updateFilter('search', value)}
            onSearch={(value) => updateFilter('search', value)}
            placeholder="Hledat inzeráty..."
          />

          {/* Ad Type */}
          <SearchableSelect
            label="Typ inzerátu"
            options={adTypeOptions}
            value={adTypeOptions.find(opt => opt.value === localFilters.adType) || null}
            onChange={(option) => updateFilter('adType', option?.value)}
            placeholder="Všechny typy"
            isClearable
            isSearchable={false}
          />

          {/* Breed - only show if we have a category */}
          {categoryId && (
            <SearchableSelect
              label="Plemeno"
              options={breedOptions}
              value={breedOptions.find(opt => opt.value === localFilters.breedId) || null}
              onChange={(option) => updateFilter('breedId', option?.value)}
              placeholder={loadingBreeds ? "Načítání..." : "Všechna plemena"}
              isClearable
              isDisabled={loadingBreeds || breedOptions.length === 0}
            />
          )}

          {/* Region */}
          <SearchableSelect
            label="Kraj"
            options={regionOptions}
            value={regionOptions.find(opt => opt.value === localFilters.regionId) || null}
            onChange={(option) => updateFilter('regionId', option?.value)}
            placeholder="Všechny kraje"
            isClearable
          />

          {/* Country */}
          <SearchableSelect
            label="Země"
            options={countryOptions}
            value={countryOptions.find(opt => opt.value === localFilters.country) || null}
            onChange={(option) => updateFilter('country', option?.value)}
            placeholder="Všechny země"
            isClearable
            isSearchable={false}
          />


        </div>
      )}
    </div>
  );
}
