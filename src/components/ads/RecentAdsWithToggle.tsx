'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdCard } from './AdCard';
import { AdListItem } from './AdListItem';
import { Button } from '@/components/ui/Button';
import { AdWithDetails } from '@/types';
import { Search, Loader2, AlertCircle, LayoutGrid, List } from 'lucide-react';

export function RecentAdsWithToggle() {
  const [ads, setAds] = useState<AdWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  const fetchRecentAds = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: '6',
        sortBy: 'created_at',
        sortOrder: 'desc',
      });

      const response = await fetch(`/api/ads?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch recent ads: ${response.status}`);
      }

      const data = await response.json();
      setAds(data.ads || []);
    } catch (err) {
      console.error('Error fetching recent ads:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecentAds();
  }, []);

  if (loading) {
    return (
      <div className="text-center">
        <div className="bg-white rounded-2xl shadow-lg p-12 border border-gray-100">
          <div className="flex justify-center items-center mb-6">
            <Loader2 className="w-12 h-12 animate-spin text-accent-600" />
          </div>
          <p className="text-gray-500 text-lg">Načítání nejnovějších inzerátů...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center">
        <div className="bg-white rounded-2xl shadow-lg p-12 border border-gray-100">
          <div className="flex justify-center items-center mb-6">
            <AlertCircle className="w-12 h-12 text-red-500" />
          </div>
          <p className="text-red-600 mb-6 text-lg">Chyba při načítání inzerátů</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              onClick={fetchRecentAds}
              className="bg-primary-600 hover:bg-primary-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
            >
              Zkusit znovu
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-accent-600 text-accent-600 hover:bg-accent-50 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" 
              asChild
            >
              <Link href="/inzerce">
                <Search className="w-5 h-5 mr-2" />
                Zobrazit všechny inzeráty
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (ads.length === 0) {
    return (
      <div className="text-center">
        <div className="bg-white rounded-2xl shadow-lg p-12 border border-gray-100">
          <div className="text-6xl mb-6">🐾</div>
          <p className="text-gray-500 mb-6 text-lg">Zatím nejsou k dispozici žádné inzeráty</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-accent-600 hover:bg-accent-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" 
              asChild
            >
              <Link href="/pridat-inzerat">
                Přidat první inzerát
              </Link>
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-accent-600 text-accent-600 hover:bg-accent-50 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" 
              asChild
            >
              <Link href="/inzerce">
                <Search className="w-5 h-5 mr-2" />
                Procházet inzeráty
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* View Mode Toggle */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center bg-white rounded-xl shadow-lg border border-gray-200 p-1">
          <button
            onClick={() => setViewMode('list')}
            className={`flex items-center px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              viewMode === 'list'
                ? 'bg-accent-600 text-white shadow-md'
                : 'text-gray-600 hover:text-accent-600 hover:bg-accent-50'
            }`}
          >
            <List className="w-4 h-4 mr-2" />
            Seznam
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`flex items-center px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              viewMode === 'grid'
                ? 'bg-accent-600 text-white shadow-md'
                : 'text-gray-600 hover:text-accent-600 hover:bg-accent-50'
            }`}
          >
            <LayoutGrid className="w-4 h-4 mr-2" />
            Mřížka
          </button>
        </div>
      </div>

      {/* Ads Display */}
      {viewMode === 'list' ? (
        <div className="space-y-6 mb-12">
          {ads.map((ad) => (
            <AdListItem key={ad.id} ad={ad} />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {ads.map((ad) => (
            <AdCard key={ad.id} ad={ad} />
          ))}
        </div>
      )}

      {/* View All Button */}
      <div className="text-center">
        <Button 
          size="lg" 
          className="bg-accent-600 hover:bg-accent-700 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" 
          asChild
        >
          <Link href="/inzerce">
            <Search className="w-5 h-5 mr-2" />
            Zobrazit všechny inzeráty
          </Link>
        </Button>
      </div>
    </div>
  );
}
