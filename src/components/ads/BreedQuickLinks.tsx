'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronRight } from 'lucide-react';

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

interface BreedQuickLinksProps {
  categoryId: number;
  categorySlug: string;
  currentBreedSlug?: string;
}

export function BreedQuickLinks({ categoryId, categorySlug, currentBreedSlug }: BreedQuickLinksProps) {
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    fetch(`/api/breeds?categoryId=${categoryId}`)
      .then(res => res.json())
      .then(data => {
        setBreeds(data);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching breeds:', error);
        setLoading(false);
      });
  }, [categoryId]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Plemena</h3>
        <div className="animate-pulse space-y-2">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (breeds.length === 0) {
    return null;
  }

  const displayedBreeds = showAll ? breeds : breeds.slice(0, 8);

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Populární plemena
      </h3>
      
      <div className="space-y-2">
        {displayedBreeds.map((breed) => (
          <Link
            key={breed.id}
            href={`/inzerce/${categorySlug}/${breed.slug}`}
            className={`flex items-center justify-between p-2 rounded-md transition-colors ${
              currentBreedSlug === breed.slug
                ? 'bg-primary-50 text-primary-700 border border-primary-200'
                : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
            }`}
          >
            <span className="text-sm font-medium">{breed.name}</span>
            <ChevronRight className="w-4 h-4 text-gray-400" />
          </Link>
        ))}
      </div>

      {breeds.length > 8 && (
        <button
          onClick={() => setShowAll(!showAll)}
          className="mt-4 text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          {showAll ? 'Zobrazit méně' : `Zobrazit všech ${breeds.length} plemen`}
        </button>
      )}
    </div>
  );
}
