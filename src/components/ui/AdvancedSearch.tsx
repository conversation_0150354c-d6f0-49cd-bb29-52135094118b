'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'popular' | 'suggestion';
  count?: number;
}

interface AdvancedSearchProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  placeholder?: string;
  suggestions?: SearchSuggestion[];
  className?: string;
}

export function AdvancedSearch({
  value,
  onChange,
  onSearch,
  placeholder = "Hledat inzeráty...",
  suggestions = [],
  className,
}: AdvancedSearchProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('infauna-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (e) {
        console.error('Failed to parse recent searches:', e);
      }
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = (searchTerm: string) => {
    if (!searchTerm.trim()) return;
    
    const updated = [
      searchTerm,
      ...recentSearches.filter(s => s !== searchTerm)
    ].slice(0, 5); // Keep only 5 recent searches
    
    setRecentSearches(updated);
    localStorage.setItem('infauna-recent-searches', JSON.stringify(updated));
  };

  const handleSearch = (searchTerm: string = value) => {
    if (searchTerm.trim()) {
      saveRecentSearch(searchTerm.trim());
      onSearch?.(searchTerm.trim());
    }
    setShowSuggestions(false);
    inputRef.current?.blur();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    onChange(suggestion.text);
    handleSearch(suggestion.text);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('infauna-recent-searches');
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const popularSuggestions: SearchSuggestion[] = [
    { id: '1', text: 'štěně', type: 'popular', count: 245 },
    { id: '2', text: 'kočka', type: 'popular', count: 189 },
    { id: '3', text: 'labrador', type: 'popular', count: 156 },
    { id: '4', text: 'zlatý retrívr', type: 'popular', count: 98 },
    { id: '5', text: 'německý ovčák', type: 'popular', count: 87 },
  ];

  const allSuggestions = [
    ...recentSearches.map(search => ({
      id: `recent-${search}`,
      text: search,
      type: 'recent' as const,
    })),
    ...popularSuggestions,
    ...suggestions,
  ];

  return (
    <div ref={containerRef} className={cn("relative", className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            setIsFocused(true);
            setShowSuggestions(true);
          }}
          placeholder={placeholder}
          icon={<Search className="w-4 h-4" />}
          className="pr-20"
        />
        
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {value && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                onChange('');
                inputRef.current?.focus();
              }}
              className="h-6 w-6 text-gray-400 hover:text-gray-600"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
          
          <Button
            onClick={() => handleSearch()}
            size="sm"
            className="h-8 px-3 text-xs"
          >
            Hledat
          </Button>
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && (isFocused || value) && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
          {allSuggestions.length > 0 ? (
            <div className="py-2">
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div className="px-3 py-2">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nedávné vyhledávání
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearRecentSearches}
                      className="text-xs text-gray-400 hover:text-gray-600 h-auto p-0"
                    >
                      Vymazat
                    </Button>
                  </div>
                  {recentSearches.map((search) => (
                    <button
                      key={`recent-${search}`}
                      onClick={() => handleSuggestionClick({
                        id: `recent-${search}`,
                        text: search,
                        type: 'recent',
                      })}
                      className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded flex items-center text-sm"
                    >
                      <Clock className="w-3 h-3 mr-2 text-gray-400" />
                      {search}
                    </button>
                  ))}
                </div>
              )}

              {/* Popular Searches */}
              {popularSuggestions.length > 0 && (
                <div className="px-3 py-2 border-t border-gray-100">
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                    Populární vyhledávání
                  </h4>
                  {popularSuggestions.map((suggestion) => (
                    <button
                      key={suggestion.id}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded flex items-center justify-between text-sm"
                    >
                      <div className="flex items-center">
                        <TrendingUp className="w-3 h-3 mr-2 text-gray-400" />
                        {suggestion.text}
                      </div>
                      {suggestion.count && (
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                          {suggestion.count}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="px-4 py-8 text-center text-gray-500">
              <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">Začněte psát pro vyhledávání</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
