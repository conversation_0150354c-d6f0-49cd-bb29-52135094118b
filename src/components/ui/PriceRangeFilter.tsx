'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { formatPrice } from '@/lib/utils';
import { DollarSign, ArrowRight } from 'lucide-react';

interface PriceRange {
  min?: number;
  max?: number;
}

interface PriceRangeFilterProps {
  value: PriceRange;
  onChange: (value: PriceRange) => void;
  currency?: 'CZK' | 'EUR';
  label?: string;
  className?: string;
}

const QUICK_RANGES = [
  { label: 'Do 1 000 Kč', min: 0, max: 1000 },
  { label: '1 000 - 5 000 Kč', min: 1000, max: 5000 },
  { label: '5 000 - 10 000 Kč', min: 5000, max: 10000 },
  { label: '10 000 - 25 000 Kč', min: 10000, max: 25000 },
  { label: '25 000+ Kč', min: 25000, max: undefined },
];

export function PriceRangeFilter({
  value,
  onChange,
  currency = 'CZK',
  label = 'Cenové rozpětí',
  className,
}: PriceRangeFilterProps) {
  const [localMin, setLocalMin] = useState(value.min?.toString() || '');
  const [localMax, setLocalMax] = useState(value.max?.toString() || '');
  const [showCustom, setShowCustom] = useState(false);

  useEffect(() => {
    setLocalMin(value.min?.toString() || '');
    setLocalMax(value.max?.toString() || '');
  }, [value]);

  const handleQuickRange = (range: typeof QUICK_RANGES[0]) => {
    onChange({
      min: range.min,
      max: range.max,
    });
    setShowCustom(false);
  };

  const handleCustomApply = () => {
    const min = localMin ? parseFloat(localMin) : undefined;
    const max = localMax ? parseFloat(localMax) : undefined;
    
    // Validate range
    if (min !== undefined && max !== undefined && min > max) {
      return; // Invalid range
    }
    
    onChange({ min, max });
  };

  const handleClear = () => {
    onChange({ min: undefined, max: undefined });
    setLocalMin('');
    setLocalMax('');
    setShowCustom(false);
  };

  const hasActiveRange = value.min !== undefined || value.max !== undefined;

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-3">
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
        {hasActiveRange && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            Vymazat
          </Button>
        )}
      </div>

      {/* Current Range Display */}
      {hasActiveRange && (
        <div className="mb-3 p-2 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center text-sm text-blue-700">
            <DollarSign className="w-4 h-4 mr-1" />
            <span>
              {value.min !== undefined ? formatPrice(value.min, currency) : '0'} 
              <ArrowRight className="w-3 h-3 mx-1 inline" />
              {value.max !== undefined ? formatPrice(value.max, currency) : '∞'}
            </span>
          </div>
        </div>
      )}

      {/* Quick Range Buttons */}
      <div className="space-y-2 mb-4">
        {QUICK_RANGES.map((range, index) => {
          const isActive = value.min === range.min && value.max === range.max;
          
          return (
            <Button
              key={index}
              variant={isActive ? "default" : "outline"}
              size="sm"
              onClick={() => handleQuickRange(range)}
              className="w-full justify-start text-sm"
            >
              {range.label}
            </Button>
          );
        })}
      </div>

      {/* Custom Range Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowCustom(!showCustom)}
        className="w-full text-sm text-gray-600 hover:text-gray-800"
      >
        {showCustom ? 'Skrýt vlastní rozsah' : 'Vlastní rozsah'}
      </Button>

      {/* Custom Range Inputs */}
      {showCustom && (
        <div className="mt-4 space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <Input
              type="number"
              placeholder="Od"
              value={localMin}
              onChange={(e) => setLocalMin(e.target.value)}
              min="0"
              step="100"
            />
            <Input
              type="number"
              placeholder="Do"
              value={localMax}
              onChange={(e) => setLocalMax(e.target.value)}
              min="0"
              step="100"
            />
          </div>
          
          <Button
            onClick={handleCustomApply}
            size="sm"
            className="w-full"
            disabled={!localMin && !localMax}
          >
            Použít rozsah
          </Button>
        </div>
      )}
    </div>
  );
}
