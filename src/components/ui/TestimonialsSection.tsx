'use client';

import { useState, useEffect } from 'react';
import { Star, Quote } from 'lucide-react';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  text: string;
  rating: number;
  petType: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    location: "Praha",
    text: "Díky Infauně jsme našli našeho krásného labradora Maxe. Celý proces byl velmi j<PERSON> a bez<PERSON>č<PERSON>ý. Doporučuji všem!",
    rating: 5,
    petType: "🐕"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>vo<PERSON>",
    location: "Brno",
    text: "Prodal jsem zde štěňata naší fenky. Skvělá komunikace s kupci a rychlé vyřízení. Určitě budu používat znovu.",
    rating: 5,
    petType: "🐕"
  },
  {
    id: 3,
    name: "<PERSON>",
    location: "Bratislava",
    text: "<PERSON><PERSON><PERSON> jsem zde svou kočičku Miu. Je to láska na první pohled! Děkuji za skvělou službu.",
    rating: 5,
    petType: "🐱"
  }
];

function TestimonialCard({ testimonial, isActive }: { testimonial: Testimonial; isActive: boolean }) {
  return (
    <div className={`transition-all duration-500 ${isActive ? 'opacity-100 scale-100' : 'opacity-50 scale-95'}`}>
      <div className="bg-white rounded-2xl shadow-xl p-8 relative">
        {/* Quote icon */}
        <div className="absolute -top-4 left-8">
          <div className="bg-accent-600 rounded-full p-3">
            <Quote className="w-6 h-6 text-white" />
          </div>
        </div>
        
        {/* Pet type emoji */}
        <div className="text-4xl mb-4 text-center">{testimonial.petType}</div>
        
        {/* Rating */}
        <div className="flex justify-center mb-4">
          {[...Array(testimonial.rating)].map((_, i) => (
            <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
          ))}
        </div>
        
        {/* Text */}
        <p className="text-gray-700 text-lg leading-relaxed mb-6 text-center italic">
          "{testimonial.text}"
        </p>
        
        {/* Author */}
        <div className="text-center">
          <div className="font-bold text-gray-900 text-lg">{testimonial.name}</div>
          <div className="text-gray-500">{testimonial.location}</div>
        </div>
      </div>
    </div>
  );
}

export function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % testimonials.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-24 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 rounded-full text-sm font-medium mb-4">
            <Star className="w-4 h-4 mr-2" />
            Recenze zákazníků
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Co říkají naši
            <span className="text-accent-600"> spokojení</span> zákazníci
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Každý den pomáháme tisícům lidí najít jejich dokonalého společníka
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={testimonial.id}
                testimonial={testimonial}
                isActive={index === activeIndex}
              />
            ))}
          </div>
          
          {/* Dots indicator */}
          <div className="flex justify-center mt-12 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeIndex 
                    ? 'bg-accent-600 scale-125' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
