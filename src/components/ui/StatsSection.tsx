'use client';

import { useEffect, useState } from 'react';
import { Users, Heart, Award, Globe } from 'lucide-react';

interface StatItemProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  delay?: number;
}

function StatItem({ icon, value, label, delay = 0 }: StatItemProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div className={`text-center transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
      <div className="flex justify-center mb-4">
        <div className="p-4 bg-accent-600/20 rounded-2xl backdrop-blur-sm">
          {icon}
        </div>
      </div>
      <div className="text-4xl md:text-5xl font-bold text-white mb-2">
        {value}
      </div>
      <div className="text-primary-200 font-medium">
        {label}
      </div>
    </div>
  );
}

export function StatsSection() {
  return (
    <section className="py-20 bg-gradient-to-r from-primary-800 to-accent-700 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white rounded-full"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-accent-300 rounded-full"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Důvěřují nám tisíce
            <span className="text-accent-300"> spokojených</span> zákazníků
          </h2>
          <p className="text-xl text-primary-200 max-w-3xl mx-auto">
            Každý den pomáháme spojovat zvířata s jejich novými rodinami
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <StatItem
            icon={<Users className="w-8 h-8 text-accent-300" />}
            value="15,000+"
            label="Aktivních uživatelů"
            delay={200}
          />
          <StatItem
            icon={<Heart className="w-8 h-8 text-accent-300" />}
            value="8,500+"
            label="Úspěšných adopcí"
            delay={400}
          />
          <StatItem
            icon={<Award className="w-8 h-8 text-accent-300" />}
            value="25,000+"
            label="Spokojených zákazníků"
            delay={600}
          />
          <StatItem
            icon={<Globe className="w-8 h-8 text-accent-300" />}
            value="3"
            label="Země v EU"
            delay={800}
          />
        </div>
      </div>
    </section>
  );
}
