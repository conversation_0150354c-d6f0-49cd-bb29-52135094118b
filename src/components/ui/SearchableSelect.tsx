'use client';

import React, { useId } from 'react';
import Select, {
  StylesConfig,
  components,
  DropdownIndicatorProps,
  ClearIndicatorProps,
  MultiValueRemoveProps,
  OptionProps,
  SingleValueProps,
  ActionMeta
} from 'react-select';
import { ChevronDown, X, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string | number;
  label: string;
  description?: string;
  count?: number;
  disabled?: boolean;
}

interface SearchableSelectProps {
  options: SelectOption[];
  value?: SelectOption | SelectOption[] | null;
  onChange: (value: SelectOption | SelectOption[] | null, actionMeta?: ActionMeta<SelectOption>) => void;
  placeholder?: string;
  isMulti?: boolean;
  isSearchable?: boolean;
  isLoading?: boolean;
  isClearable?: boolean;
  isDisabled?: boolean;
  label?: string;
  error?: string;
  className?: string;
  noOptionsMessage?: string;
}

// Custom components
const DropdownIndicator = (props: DropdownIndicatorProps) => (
  <components.DropdownIndicator {...props}>
    <ChevronDown className="w-4 h-4 text-gray-400" />
  </components.DropdownIndicator>
);

const ClearIndicator = (props: ClearIndicatorProps) => (
  <components.ClearIndicator {...props}>
    <X className="w-4 h-4 text-gray-400 hover:text-gray-600" />
  </components.ClearIndicator>
);

const MultiValueRemove = (props: MultiValueRemoveProps) => (
  <components.MultiValueRemove {...props}>
    <X className="w-3 h-3" />
  </components.MultiValueRemove>
);

const Option = (props: OptionProps<SelectOption>) => {
  const { data, isSelected, isFocused } = props;
  
  return (
    <components.Option {...props}>
      <div className="flex items-center justify-between w-full">
        <div className="flex flex-col">
          <span className={cn(
            "text-sm",
            isSelected ? "font-medium" : "font-normal"
          )}>
            {data.label}
          </span>
          {data.description && (
            <span className="text-xs text-gray-500 mt-0.5">
              {data.description}
            </span>
          )}
        </div>
        {data.count !== undefined && (
          <span className={cn(
            "text-xs px-2 py-0.5 rounded-full",
            isSelected || isFocused 
              ? "bg-white/20 text-white" 
              : "bg-gray-100 text-gray-600"
          )}>
            {data.count}
          </span>
        )}
      </div>
    </components.Option>
  );
};

const SingleValue = (props: SingleValueProps<SelectOption>) => {
  const { data } = props;
  
  return (
    <components.SingleValue {...props}>
      <div className="flex items-center">
        <span className="text-sm">{data.label}</span>
        {data.count !== undefined && (
          <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">
            {data.count}
          </span>
        )}
      </div>
    </components.SingleValue>
  );
};

// Custom styles
const customStyles: StylesConfig<SelectOption, boolean> = {
  control: (provided, state) => ({
    ...provided,
    minHeight: '40px',
    border: state.isFocused 
      ? '2px solid rgb(59 130 246)' 
      : '1px solid rgb(209 213 219)',
    borderRadius: '6px',
    boxShadow: state.isFocused 
      ? '0 0 0 1px rgb(59 130 246)' 
      : 'none',
    '&:hover': {
      borderColor: state.isFocused 
        ? 'rgb(59 130 246)' 
        : 'rgb(156 163 175)',
    },
  }),
  menu: (provided) => ({
    ...provided,
    borderRadius: '8px',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    border: '1px solid rgb(229 231 235)',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected 
      ? 'rgb(59 130 246)' 
      : state.isFocused 
      ? 'rgb(239 246 255)' 
      : 'white',
    color: state.isSelected ? 'white' : 'rgb(17 24 39)',
    padding: '8px 12px',
    cursor: 'pointer',
    '&:active': {
      backgroundColor: state.isSelected 
        ? 'rgb(59 130 246)' 
        : 'rgb(219 234 254)',
    },
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: 'rgb(239 246 255)',
    borderRadius: '4px',
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: 'rgb(59 130 246)',
    fontSize: '14px',
  }),
  multiValueRemove: (provided) => ({
    ...provided,
    color: 'rgb(59 130 246)',
    '&:hover': {
      backgroundColor: 'rgb(59 130 246)',
      color: 'white',
    },
  }),
  placeholder: (provided) => ({
    ...provided,
    color: 'rgb(156 163 175)',
    fontSize: '14px',
  }),
  noOptionsMessage: (provided) => ({
    ...provided,
    color: 'rgb(107 114 128)',
    fontSize: '14px',
    padding: '12px',
  }),
  loadingMessage: (provided) => ({
    ...provided,
    color: 'rgb(107 114 128)',
    fontSize: '14px',
  }),
};

export function SearchableSelect({
  options,
  value,
  onChange,
  placeholder = "Vyberte...",
  isMulti = false,
  isSearchable = true,
  isLoading = false,
  isClearable = true,
  isDisabled = false,
  label,
  error,
  className,
  noOptionsMessage = "Žádné možnosti",
}: SearchableSelectProps) {
  const id = useId();

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      
      <Select
        instanceId={id}
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isMulti={isMulti}
        isSearchable={isSearchable}
        isLoading={isLoading}
        isClearable={isClearable}
        isDisabled={isDisabled}
        styles={customStyles}
        components={{
          DropdownIndicator,
          ClearIndicator,
          MultiValueRemove,
          Option,
          SingleValue,
        }}
        noOptionsMessage={() => noOptionsMessage}
        loadingMessage={() => "Načítání..."}
        className="react-select-container"
        classNamePrefix="react-select"
      />
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
