import React from 'react';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Textarea } from '@/components/ui/Textarea';
import { useCategories } from '@/hooks/useCategories';
import { useBreeds } from '@/hooks/useBreeds';
import { useRegions } from '@/hooks/useRegions';
import { AdFormData, AdFormErrors } from '@/types/ad-form';
import { 
  FileText, 
  Tag, 
  MapPin, 
  Globe, 
  DollarSign, 
  Phone,
  PawPrint 
} from 'lucide-react';

interface BasicInfoStepProps {
  data: AdFormData;
  errors: AdFormErrors;
  onChange: (field: keyof AdFormData, value: any) => void;
}

export function BasicInfoStep({ data, errors, onChange }: BasicInfoStepProps) {
  const { categories, loading: categoriesLoading } = useCategories();
  const { breeds, loading: breedsLoading } = useBreeds(data.categoryId);
  const { regions, loading: regionsLoading } = useRegions();

  const adTypeOptions = [
    { value: 'prodám', label: 'Prodám' },
    { value: 'koupím', label: 'Koupím' },
    { value: 'daruji', label: 'Daruji' },
  ];

  const countryOptions = [
    { value: 'ČR', label: 'Česká republika' },
    { value: 'SR', label: 'Slovensko' },
    { value: 'EU', label: 'EU' },
  ];

  const priceTypeOptions = [
    { value: 'CZK', label: 'CZK' },
    { value: 'EUR', label: 'EUR' },
    { value: 'Dohodou', label: 'Dohodou' },
    { value: 'V textu', label: 'V textu' },
  ];

  const categoryOptions = categories.map(cat => ({
    value: cat.id,
    label: cat.name,
  }));

  const breedOptions = breeds.map(breed => ({
    value: breed.id,
    label: breed.name,
  }));

  const regionOptions = regions.map(region => ({
    value: region.id,
    label: region.name,
  }));

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Základní informace o inzerátu
        </h2>
        <p className="text-gray-600">
          Vyplňte všechny povinné údaje označené hvězdičkou
        </p>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Title and Ad Type */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Nadpis inzerátu"
            placeholder="Např. Štěně Labradorského retrívra"
            icon={<FileText className="w-4 h-4" />}
            value={data.title}
            onChange={(e) => onChange('title', e.target.value)}
            error={errors.title}
            required
          />
          
          <Select
            label="Typ inzerátu"
            placeholder="Vyberte typ"
            options={adTypeOptions}
            value={data.adType || ''}
            onChange={(e) => onChange('adType', e.target.value)}
            error={errors.adType}
            required
          />
        </div>

        {/* Location */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Země"
            placeholder="Vyberte zemi"
            options={countryOptions}
            value={data.country || ''}
            onChange={(e) => onChange('country', e.target.value)}
            error={errors.country}
            required
          />
          
          <Select
            label="Kraj/Region"
            placeholder="Vyberte kraj"
            options={regionOptions}
            value={data.regionId || ''}
            onChange={(e) => onChange('regionId', e.target.value ? parseInt(e.target.value) : undefined)}
            error={errors.regionId}
            loading={regionsLoading}
          />
        </div>

        {/* Category and Breed */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Kategorie"
            placeholder="Vyberte kategorii"
            options={categoryOptions}
            value={data.categoryId || ''}
            onChange={(e) => {
              const categoryId = parseInt(e.target.value);
              onChange('categoryId', categoryId);
              onChange('breedId', undefined); // Reset breed when category changes
            }}
            error={errors.categoryId}
            loading={categoriesLoading}
            required
          />
          
          <Select
            label="Plemeno/Druh"
            placeholder="Vyberte plemeno"
            options={breedOptions}
            value={data.breedId || ''}
            onChange={(e) => onChange('breedId', e.target.value ? parseInt(e.target.value) : undefined)}
            error={errors.breedId}
            loading={breedsLoading}
            disabled={!data.categoryId}
          />
        </div>

        {/* Price */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Typ ceny"
            placeholder="Vyberte typ ceny"
            options={priceTypeOptions}
            value={data.priceType || ''}
            onChange={(e) => onChange('priceType', e.target.value)}
            error={errors.priceType}
          />
          
          {(data.priceType === 'CZK' || data.priceType === 'EUR') && (
            <Input
              label="Částka"
              type="number"
              placeholder="0"
              min="0"
              step="0.01"
              icon={<DollarSign className="w-4 h-4" />}
              value={data.priceAmount || ''}
              onChange={(e) => onChange('priceAmount', e.target.value ? parseFloat(e.target.value) : undefined)}
              error={errors.priceAmount}
            />
          )}
        </div>

        {/* Description */}
        <Textarea
          label="Popis"
          placeholder="Detailní popis zvířete, jeho vlastnosti, zdravotní stav, atd."
          rows={6}
          maxLength={2000}
          value={data.description}
          onChange={(e) => onChange('description', e.target.value)}
          error={errors.description}
          required
        />

        {/* Contact */}
        <Input
          label="Kontaktní telefon"
          type="tel"
          placeholder="+420 123 456 789"
          icon={<Phone className="w-4 h-4" />}
          value={data.contactPhone}
          onChange={(e) => onChange('contactPhone', e.target.value)}
          error={errors.contactPhone}
          required
        />
      </div>
    </div>
  );
}
