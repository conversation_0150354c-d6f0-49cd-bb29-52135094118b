export interface AdFormData {
  // Basic info
  title: string;
  adType: 'prodám' | 'koupím' | 'daruji';
  description: string;
  
  // Location
  country: 'ČR' | 'SR' | 'EU';
  regionId?: number;
  
  // Category & Breed
  categoryId: number;
  breedId?: number;
  
  // Price
  priceType?: 'CZK' | 'EUR' | 'Dohodou' | 'V textu';
  priceAmount?: number;
  
  // Contact
  contactPhone: string;
}

export interface AdFormErrors {
  title?: string;
  adType?: string;
  description?: string;
  country?: string;
  regionId?: string;
  categoryId?: string;
  breedId?: string;
  priceType?: string;
  priceAmount?: string;
  contactPhone?: string;
}

export interface AdFormStep {
  id: string;
  title: string;
  description: string;
  isValid: (data: AdFormData) => boolean;
}

export const AD_FORM_STEPS: AdFormStep[] = [
  {
    id: 'basic-info',
    title: 'Základní údaje',
    description: 'Vyplňte informace o inzerátu',
    isValid: (data) => {
      return !!(
        data.title &&
        data.adType &&
        data.description &&
        data.country &&
        data.categoryId &&
        data.contactPhone
      );
    },
  },
  {
    id: 'images',
    title: 'Fotografie',
    description: 'Přidejte fotografie',
    isValid: () => true, // Images are optional
  },
];
