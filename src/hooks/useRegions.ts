import { useState, useEffect } from 'react';
import { Region } from '@/db/schema';

export function useRegions() {
  const [regions, setRegions] = useState<Region[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/regions');
        if (!response.ok) {
          throw new Error('Failed to fetch regions');
        }
        
        const data = await response.json();
        setRegions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchRegions();
  }, []);

  return { regions, loading, error };
}
