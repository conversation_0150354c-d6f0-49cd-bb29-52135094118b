import { useState, useEffect } from 'react';
import { Breed } from '@/db/schema';

export function useBreeds(categoryId?: number) {
  const [breeds, setBreeds] = useState<Breed[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!categoryId) {
      setBreeds([]);
      return;
    }

    const fetchBreeds = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/breeds?categoryId=${categoryId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch breeds');
        }
        
        const data = await response.json();
        setBreeds(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchBreeds();
  }, [categoryId]);

  return { breeds, loading, error };
}
