import { AdFilters } from '@/types';

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface Breed {
  id: number;
  name: string;
  slug: string;
  categoryId: number;
}

/**
 * Generate URL for ads listing based on filters
 */
export function generateAdsUrl(filters: AdFilters, categories?: Category[], breeds?: Breed[]): string {
  // If no category filter, return base ads URL
  if (!filters.categoryId) {
    return '/inzerce';
  }

  // Find category by ID
  const category = categories?.find(c => c.id === filters.categoryId);
  if (!category) {
    return '/inzerce';
  }

  // If no breed filter, return category URL
  if (!filters.breedId) {
    return `/inzerce/${category.slug}`;
  }

  // Find breed by ID
  const breed = breeds?.find(b => b.id === filters.breedId && b.categoryId === filters.categoryId);
  if (!breed) {
    return `/inzerce/${category.slug}`;
  }

  // Return category + breed URL
  return `/inzerce/${category.slug}/${breed.slug}`;
}

/**
 * Parse URL parameters to extract filters
 */
export function parseUrlToFilters(
  categorySlug?: string, 
  breedSlug?: string, 
  categories?: Category[], 
  breeds?: Breed[]
): AdFilters {
  const filters: AdFilters = {};

  if (categorySlug && categories) {
    const category = categories.find(c => c.slug === categorySlug);
    if (category) {
      filters.categoryId = category.id;

      if (breedSlug && breeds) {
        const breed = breeds.find(b => b.slug === breedSlug && b.categoryId === category.id);
        if (breed) {
          filters.breedId = breed.id;
        }
      }
    }
  }

  return filters;
}

/**
 * Generate breadcrumb items for navigation
 */
export interface BreadcrumbItem {
  label: string;
  href: string;
  isActive?: boolean;
}

export function generateBreadcrumbs(
  categorySlug?: string,
  breedSlug?: string,
  categories?: Category[],
  breeds?: Breed[]
): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Domů', href: '/' },
    { label: 'Inzeráty', href: '/inzerce' }
  ];

  if (categorySlug && categories) {
    const category = categories.find(c => c.slug === categorySlug);
    if (category) {
      breadcrumbs.push({
        label: category.name,
        href: `/inzerce/${category.slug}`,
        isActive: !breedSlug
      });

      if (breedSlug && breeds) {
        const breed = breeds.find(b => b.slug === breedSlug && b.categoryId === category.id);
        if (breed) {
          breadcrumbs.push({
            label: breed.name,
            href: `/inzerce/${category.slug}/${breed.slug}`,
            isActive: true
          });
        }
      }
    }
  }

  return breadcrumbs;
}
